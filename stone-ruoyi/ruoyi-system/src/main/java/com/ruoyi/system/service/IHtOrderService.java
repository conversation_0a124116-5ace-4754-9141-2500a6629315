package com.ruoyi.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.AppUser;
import com.ruoyi.system.domain.HtOrder;
import com.ruoyi.system.domain.HtOrderRecord;
import com.ruoyi.system.domain.vo.HtGoodsItem;
import com.ruoyi.system.domain.vo.HtOrderRecordVo;
import com.ruoyi.system.domain.vo.HtOrderSystemVo;
import com.ruoyi.system.domain.vo.HtOrderVo;
import org.springframework.web.bind.annotation.RequestParam;

import java.text.ParseException;
import java.util.HashMap;
import java.util.List;

/**
 * 订单Service接口
 *
 * <AUTHOR>
 * @date 2024-11-23
 */
public interface IHtOrderService extends IService<HtOrder> {
    /**
     * 查询订单
     *
     * @param id 订单主键
     * @return 订单
     */
    public HtOrder selectHtOrderById(Long id);

    /**
     * 查询订单列表
     *
     * @param htOrder 订单
     * @return 订单集合
     */
    public List<HtOrder> selectHtOrderList(HtOrder htOrder);

    public List<HtOrderSystemVo> selectSysHtOrderList(HtOrder htOrder);

    /**
     * 新增订单
     *
     * @param htOrder 订单
     * @return 结果
     */
    public int insertHtOrder(HtOrder htOrder);

    /**
     * 修改订单
     *
     * @param htOrder 订单
     * @return 结果
     */
    public int updateHtOrder(HtOrder htOrder);

    /**
     * 批量删除订单
     *
     * @param ids 需要删除的订单主键集合
     * @return 结果
     */
    public int deleteHtOrderByIds(Long[] ids);

    /**
     * 删除订单信息
     *
     * @param id 订单主键
     * @return 结果
     */
    public int deleteHtOrderById(Long id);

    HashMap<String, Object> getTodayMoney(Integer userId);

    HashMap<String, Object>  getPastMoney(Long userId) ;

    AjaxResult updateOrderType(HtOrder htOrder);

    HtOrderVo selectOrderById(Long id);

    /**
     * 获取用户的待付款列表
     *
     * @param userId 用户主键
     * @return 待付款信息
     */
    List<HtOrderRecord> getPayInfo(Integer userId);

    AjaxResult confirmPayment(Integer recordId);

    /**
     * 查询抢购明细列表
     *
     * @param htOrder 查询条件
     * @return 抢购商品列表
     */
    public List<HtGoodsItem> selectGrabOrderList(HtOrder htOrder);

    /**
     * 查询抢购明细列表
     *
     * @param htOrder 查询条件
     * @return 抢购商品列表
     */
    public List<HtOrder> selectHtOrderGrabList(HtOrder htOrder);



    List<AppUser> grabUser();
}

