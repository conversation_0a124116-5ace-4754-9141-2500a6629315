package com.ruoyi.system.enums;

/**
 * 订单状态枚举
 * 
 * <AUTHOR>
 * @date 2024-12-11
 */
public enum OrderStatusEnum {

    /**
     * 订单创建成功 待审核
     */
    PENDING_REVIEW(1L, "订单创建成功 待审核"),

    /**
     * 订单分配完成 待完成订单
     */
    ALLOCATION_COMPLETED(2L, "订单分配完成 待完成订单"),

    /**
     * 订单已支付 待确认收款
     */
    PAID_PENDING_CONFIRMATION(3L, "订单已支付 待确认收款"),

    /**
     * 订单已确认收款 待委托上架
     */
    CONFIRMED_PENDING_CONSIGNMENT(4L, "订单已确认收款 待委托上架"),

    /**
     * 订单委托上架成功 待上架
     */
    CONSIGNMENT_SUCCESS_PENDING_LISTING(5L, "订单委托上架成功 待上架"),

    /**
     * 订单上架成功
     */
    LISTING_SUCCESS(6L, "订单上架成功");
    
    private final Long code;
    private final String description;
    
    OrderStatusEnum(Long code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public Long getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
}
