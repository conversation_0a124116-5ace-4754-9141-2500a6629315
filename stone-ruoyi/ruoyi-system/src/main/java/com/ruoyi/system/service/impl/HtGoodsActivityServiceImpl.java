package com.ruoyi.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.ruoyi.common.utils.DateUtils; //需要修改成自己的包名
import com.ruoyi.system.domain.HtFeeRevenueConfig;
import com.ruoyi.system.domain.HtGoods;
import com.ruoyi.system.mapper.HtGoodsMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.HtGoodsActivityMapper;
import com.ruoyi.system.domain.HtGoodsActivity;
import com.ruoyi.system.service.IHtGoodsActivityService;


/**
 * 商品活动管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-21
 */
@Service
public class HtGoodsActivityServiceImpl extends ServiceImpl<HtGoodsActivityMapper,HtGoodsActivity> implements IHtGoodsActivityService {
    @Autowired
    private HtGoodsActivityMapper htGoodsActivityMapper;
    @Autowired
    private HtGoodsMapper htGoodsMapper;
    @Autowired
    private ComputingConfigUtils computingConfigUtils;
    /**
     * 查询商品活动管理
     *
     * @param id 商品活动管理主键
     * @return 商品活动管理
     */
    @Override
    public HtGoodsActivity selectHtGoodsActivityById(Long id) {
        return htGoodsActivityMapper.selectHtGoodsActivityById(id);
    }

    /**
     * 查询商品活动管理列表
     *
     * @param htGoodsActivity 商品活动管理
     * @return 商品活动管理
     */
    @Override
    public List<HtGoodsActivity> selectHtGoodsActivityList(HtGoodsActivity htGoodsActivity) {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        List<HtGoodsActivity> htGoodsActivities = htGoodsActivityMapper.selectHtGoodsActivityList(htGoodsActivity);


        return htGoodsActivities.stream().map(item -> {


            // 获取当前日期的星期几
            DayOfWeek dayOfWeek = currentDate.getDayOfWeek();
            if (dayOfWeek != DayOfWeek.SATURDAY && dayOfWeek != DayOfWeek.SUNDAY) {
                item.setStatus(0);
            }
            // 定义时间范围
            // 获取当前时间
            LocalTime currentTime = LocalTime.now();
            // 从配置中获取时间范围
            HtFeeRevenueConfig config = computingConfigUtils.getHtFeeRevenueConfig();

            LocalTime startTime = LocalTime.parse(config.getStartTime());

            item.setRemark(config.getStartTime());
            if (currentTime.isAfter(startTime)) {
                item.setStatus(1);
            }else {
                item.setStatus(0);
            }
            List<HtGoods> goodsList = htGoodsMapper.selectList(Wrappers.<HtGoods>query()
                    .eq("is_show", 1)
                    .eq("is_del", 0)
                    .eq("activity_id", 5));
            if(goodsList.isEmpty()){
                item.setStatus(0);
            }
            return item;
        }).collect(Collectors.toList());
    }

    /**
     * 新增商品活动管理
     *
     * @param htGoodsActivity 商品活动管理
     * @return 结果
     */
    @Override
    public int insertHtGoodsActivity(HtGoodsActivity htGoodsActivity) {
                htGoodsActivity.setCreateTime(DateUtils.getNowDate());
            return htGoodsActivityMapper.insertHtGoodsActivity(htGoodsActivity);
    }

    /**
     * 修改商品活动管理
     *
     * @param htGoodsActivity 商品活动管理
     * @return 结果
     */
    @Override
    public int updateHtGoodsActivity(HtGoodsActivity htGoodsActivity) {
                htGoodsActivity.setUpdateTime(DateUtils.getNowDate());
        return htGoodsActivityMapper.updateHtGoodsActivity(htGoodsActivity);
    }

    /**
     * 批量删除商品活动管理
     *
     * @param ids 需要删除的商品活动管理主键
     * @return 结果
     */
    @Override
    public int deleteHtGoodsActivityByIds(Long[] ids) {
        return htGoodsActivityMapper.deleteHtGoodsActivityByIds(ids);
    }

    /**
     * 删除商品活动管理信息
     *
     * @param id 商品活动管理主键
     * @return 结果
     */
    @Override
    public int deleteHtGoodsActivityById(Long id) {
        return htGoodsActivityMapper.deleteHtGoodsActivityById(id);
    }
}

