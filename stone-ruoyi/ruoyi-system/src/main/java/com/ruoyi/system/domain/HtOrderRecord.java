package com.ruoyi.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/12/2 17:04
 * @description: 订单记录表
 */
@Data
@TableName("ht_order_record")
public class HtOrderRecord {

    /** 订单付款记录表 */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /** 关联订单主键 */
    private Integer orderId;

    /** 付款用户主键 */
    private Integer userId;

    /** 付款人名称 */
    @TableField(exist = false)
    private String payName;

    /** 付款人手机号 */
    @TableField(exist = false)
    private String payPhone;

    /** 付款凭证图片地址 */
    @Excel(name = "付款凭证图片地址")
    private String paymentProof;

    /** 收款人主键 */
    private Integer recipientId;

    /** 收款人姓名 */
    @Excel(name = "收款人姓名")
    private String recipientName;

    /** 收款人联系电话 */
    @Excel(name = "收款人联系电话")
    private String recipientPhone;

    /** 付款金额 */
    @Excel(name = "付款金额")
    private BigDecimal amount;

    /** 是否确认付款 0未确认 1确认收款 */
    private Integer type;

    /** 创建订单时间 */
    private Date createTime;

    /** 关联订单状态 */
    @TableField(exist = false)
    private Long orderStatus;

    @TableField(exist = false)
    private HtPaymentAgreements bank;

    @TableField(exist = false)
    private HtPaymentAgreements wechat;

    @TableField(exist = false)
    private HtPaymentAgreements pay;

}
