package com.ruoyi.system.service.impl;

import com.alipay.api.domain.Goods;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.*;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.AppUser;
import com.ruoyi.system.domain.HtGoods;
import com.ruoyi.system.domain.HtOrder;
import com.ruoyi.system.domain.HtUserShares;
import com.ruoyi.system.domain.vo.HtGoodsItem;
import com.ruoyi.system.domain.vo.HtOrderRecordVo;
import com.ruoyi.system.domain.vo.HtOrderSystemVo;
import com.ruoyi.system.domain.vo.HtOrderVo;
import com.ruoyi.system.enums.OrderStatusEnum;
import com.ruoyi.system.mapper.*;
import com.ruoyi.system.service.IHtOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-23
 */

@Slf4j
@Service
public class HtOrderServiceImpl extends ServiceImpl<HtOrderMapper, HtOrder> implements IHtOrderService {
    @Autowired
    private HtOrderMapper htOrderMapper;

    @Autowired
    private HtGoodsMapper goodsMapper;

    @Autowired
    private AppUserMapper appUserMapper;
    @Autowired
    private RedisCache redisCache;

    @Autowired
    private HtOrderRecordMapper htOrderRecordMapper;
    @Autowired
    private HtPaymentAgreementsMapper htPaymentAgreementsMapper;


    /**
     * 查询订单
     *
     * @param id 订单主键
     * @return 订单
     */
    @Override
    public HtOrder selectHtOrderById(Long id) {
        return htOrderMapper.selectHtOrderById(id);
    }

    /**
     * 查询订单列表
     *
     * @param htOrder 订单
     * @return 订单
     */
    @Override
    public List<HtOrder> selectHtOrderList(HtOrder htOrder) {
        return htOrderMapper.selectHtOrderList(htOrder);
    }

    /**
     * 查询抢购明细列表
     *
     * @param htOrder 查询条件
     * @return 抢购商品列表
     */
    public List<HtOrder> selectHtOrderGrabList(HtOrder htOrder){
        List<HtOrder> list=htOrderMapper.selectHtOrderList(htOrder);
        for(HtOrder data:list){
            HtGoods htGoods= goodsMapper.selectHtGoodsById(data.getGoodsId());
            AppUser appUser=appUserMapper.selectAppUserById(data.getUserId());
            if(StringUtils.isNotNull(appUser)){
                data.setUserName(appUser.getRealName());
            }
            data.setHtGoods(htGoods);
        }
        return list;
    }


    @Override
    public List<HtOrderSystemVo> selectSysHtOrderList(HtOrder htOrder) {
        return htOrderMapper.selectSysHtOrderList(htOrder);
    }

    /**
     * 新增订单
     *
     * @param htOrder 订单
     * @return 结果
     */
    @Override
    public int insertHtOrder(HtOrder htOrder) {
        return htOrderMapper.insertHtOrder(htOrder);
    }

    /**
     * 修改订单
     *
     * @param htOrder 订单
     * @return 结果
     */
    @Override
    public int updateHtOrder(HtOrder htOrder) {
        return htOrderMapper.updateHtOrder(htOrder);
    }

    /**
     * 批量删除订单
     *
     * @param ids 需要删除的订单主键
     * @return 结果
     */
    @Override
    public int deleteHtOrderByIds(Long[] ids) {
        return htOrderMapper.deleteHtOrderByIds(ids);
    }

    /**
     * 删除订单信息
     *
     * @param id 订单主键
     * @return 结果
     */
    @Override
    public int deleteHtOrderById(Long id) {
        return htOrderMapper.deleteHtOrderById(id);
    }

    @Override
    public HashMap<String, Object> getTodayMoney(Integer userId) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("payMoney", htOrderRecordMapper.getTodayPayMoney(userId));
        map.put("RevenueMoney", htOrderRecordMapper.getTodayRevenueMoney(userId));
        return map;
    }

    @Override
    public HashMap<String, Object> getPastMoney(Long userId) {
        HashMap<String, Object> res = new HashMap<>();

        List<HtOrderRecordVo> payListVo = new ArrayList<>();
        List<HtOrderRecordVo> revenueListVo = new ArrayList<>();
        HashMap<String, ArrayList> payMap = new HashMap<>();
        HashMap<String, ArrayList> revenueMap = new HashMap<>();
        //支出
        List<HtOrderRecord> payList = htOrderRecordMapper.selectOldPayList(userId);
        if (payList.size() > 0) {
            for (HtOrderRecord htOrder : payList) {
                String orderDate = DateUtils.parseDateToStr("yyyy-MM-dd", htOrder.getCreateTime());
                if (payMap.get(orderDate) != null) {
                    payMap.get(orderDate).add(htOrder);
                } else {
                    ArrayList<HtOrderRecord> list = new ArrayList<>();
                    list.add(htOrder);
                    payMap.put(orderDate, list);
                }
            }
            for (String key : payMap.keySet()) {
                HtOrderRecordVo htOrderVo = new HtOrderRecordVo();
                ArrayList list = payMap.get(key);
                htOrderVo.setDate(key);
                htOrderVo.setOrderList(list);
                payListVo.add(htOrderVo);
            }
        }


        List<HtOrderRecord> revenueList = htOrderRecordMapper.selectRevenueList(userId);
        if (revenueList.size() > 0) {
            for (HtOrderRecord htOrder : revenueList) {
                String orderDate = DateUtils.parseDateToStr("yyyy-MM-dd", htOrder.getCreateTime());
                if (revenueMap.get(orderDate) != null) {
                    revenueMap.get(orderDate).add(htOrder);
                } else {
                    ArrayList<HtOrderRecord> list = new ArrayList<>();
                    list.add(htOrder);
                    revenueMap.put(orderDate, list);
                }
            }
            for (String key : revenueMap.keySet()) {
                HtOrderRecordVo htOrderVo = new HtOrderRecordVo();
                ArrayList list = revenueMap.get(key);
                htOrderVo.setDate(key);
                htOrderVo.setOrderList(list);
                revenueListVo.add(htOrderVo);
            }
        }

        res.put("payListVo", payListVo);
        res.put("revenueListVo", revenueListVo);
        return res;
    }



    @Override
    @Transactional
    public AjaxResult updateOrderType(HtOrder htOrder) {
        HtOrder order = htOrderMapper.selectHtOrderById(htOrder.getId());
        //更新当前付款信息
        for (PaymentProof paymentProof : htOrder.getPaymentProofList()) {
            String image = paymentProof.getImage().stream().map(String::valueOf).collect(Collectors.joining(","));
            HtOrderRecord htOrderRecord=new HtOrderRecord();
            htOrderRecord.setPaymentProof(image);
            htOrderRecord.setId(paymentProof.getOrderRecordId());
            htOrderRecordMapper.updateById(htOrderRecord);
        }
        // 当前用户的订单已经结束
        order.setOrderStatus(OrderStatusEnum.PAID_PENDING_CONFIRMATION.getCode());
        htOrderMapper.updateHtOrder(order);
        return AjaxResult.success();
    }

    //封装收益
    private HtIncome getHtIncome(HtGoods goods) {
        HtIncome htIncome = new HtIncome();
        htIncome.setGoodsId(goods.getId().intValue());
        htIncome.setUserId(goods.getUserId());
        htIncome.setGoodsImg(goods.getCateId());
        htIncome.setResalePrice(goods.getResalePrice());
        htIncome.setPrice(goods.getPrice());
        htIncome.setGoodsName(goods.getStoreName());
        htIncome.setMyRevenue(goods.getResalePrice().subtract(goods.getPrice()));
        return htIncome;
    }

    @Override
    public HtOrderVo selectOrderById(Long id) {
        HtOrder htOrder = htOrderMapper.selectHtOrderById(id);
        HtOrderVo htOrderVo = new HtOrderVo();
        BeanUtils.copyProperties(htOrder, htOrderVo);

        HtGoods htGoods = goodsMapper.selectHtGoodsById(htOrder.getGoodsId());
        BigDecimal multiply = htGoods.getPrice().multiply(new BigDecimal(0.035));
        multiply = multiply.setScale(0, RoundingMode.HALF_UP);
        htOrderVo.setMoneyNum(multiply);
        htOrderVo.setNum(1);
        htOrderVo.setPrice(htGoods.getResalePrice());
        redisCache.setCacheObject("goods:" + htGoods.getId(), multiply);
        htOrderVo.setGoodsName(htGoods.getStoreName());
        return htOrderVo;
    }

    /**
     * 获取用户的待付款列表
     *
     * @param userId 用户主键
     * @return 待付款信息
     */
    @Override
    public List<HtOrderRecord> getPayInfo(Integer userId) {
        //拿到需要付款的图片
        List<HtOrderRecord> list = htOrderRecordMapper.selectList(Wrappers.<HtOrderRecord>query()
                .eq("user_id", userId)
                .like("create_time", DateUtils.parseDateToStr("yyyy-MM-dd", new Date()))
        );
        for (HtOrderRecord htOrderRecord : list) {
            Integer recipientId = htOrderRecord.getRecipientId();
            HtPaymentAgreements agreements = new HtPaymentAgreements();
            agreements.setMethodId(1L);
            agreements.setUserId(recipientId.longValue());
            List<HtPaymentAgreements> bankList = htPaymentAgreementsMapper.selectHtPaymentAgreementsList(agreements);
            if (bankList.size() > 0) {
                HtPaymentAgreements bank = bankList.get(0);
                htOrderRecord.setBank(bank);
                agreements.setMethodId(2L);
            }
            List<HtPaymentAgreements> wechatList = htPaymentAgreementsMapper.selectHtPaymentAgreementsList(agreements);
            if (wechatList.size() > 0) {
                HtPaymentAgreements wechat = wechatList.get(0);
                htOrderRecord.setWechat(wechat);
                agreements.setMethodId(3L);
            }
            List<HtPaymentAgreements> payList = htPaymentAgreementsMapper.selectHtPaymentAgreementsList(agreements);
            if (payList.size() > 0) {
                HtPaymentAgreements pay = payList.get(0);
                htOrderRecord.setPay(pay);
            }
        }
        return list;
    }


    //确认收款
    @Override
    @Transactional
    public AjaxResult confirmPayment(Integer recordId) {

        //当前订单确认收款
        HtOrderRecord htOrderRecord = htOrderRecordMapper.selectById(recordId);
        if(StringUtils.isNull(htOrderRecord)){
            return AjaxResult.error("订单信息有误，无法确认收款");
        }
        htOrderRecord.setType(1);
        htOrderRecordMapper.updateById(htOrderRecord);


        //查询当前付款人是否还存在其他的付款记录未确认收款
        Long count=htOrderRecordMapper.selectCount(Wrappers.<HtOrderRecord>query().eq("user_id",  htOrderRecord.getUserId()).eq("type", 0).ne("id", htOrderRecord.getId()));
        if(count==0){
            //查询今天当前购买人的记录
            HtOrder order=htOrderMapper.selectOne(Wrappers.<HtOrder>query().eq("user_id",  htOrderRecord.getUserId()).like("create_time",DateUtils.getDate()).last("limit 1").orderByDesc("create_time"));
            HtGoods goodsRes = new HtGoods();
            //修改商品表中的持有人
            goodsRes.setId(order.getGoodsId());
            goodsRes.setUserId(order.getUserId().intValue());
            goodsMapper.updateHtGoods(goodsRes);
            if (!(order.getOrderStatus() == 5 || order.getOrderStatus() == 6)) {
                order.setUpdatedAt(DateUtils.getNowDate());
                order.setOrderStatus(OrderStatusEnum.CONFIRMED_PENDING_CONSIGNMENT.getCode());
                htOrderMapper.updateById(order);
            }
        }
        //如果当前订单的收款人，同时也需要付款，且订单为付款状态，则直接修改订单
        List<HtOrder> htOrders= htOrderMapper.selectList(Wrappers.<HtOrder>query().eq("user_id", htOrderRecord.getRecipientId()).eq("order_status", OrderStatusEnum.PAID_PENDING_CONFIRMATION.getCode()));
        for(HtOrder htOrder:htOrders){
            htOrder.setUpdatedAt(DateUtils.getNowDate());
            htOrder.setOrderStatus(OrderStatusEnum.CONFIRMED_PENDING_CONSIGNMENT.getCode());
            htOrderMapper.updateById(htOrder);
            HtGoods goods = new HtGoods();
            //修改商品表中的持有人
            goods.setId(htOrder.getGoodsId());
            goods.setUserId(htOrder.getUserId().intValue());
            goodsMapper.updateHtGoods(goods);
        }
        //修改订单状态为已付款
        return AjaxResult.success();
    }

    @Override
    public List<HtGoodsItem> selectGrabOrderList(HtOrder htOrder) {
        // 构建查询条件
        HtGoods query = new HtGoods();
        query.setIsBest(1);
        query.setIsNew(1);
        // 查询商品列表（这里会进行分页）
        List<HtGoods> goodsList = goodsMapper.selectHtGoodsList(query);
        if (goodsList == null || goodsList.isEmpty()) {
            return new ArrayList<>();
        }
        // 获取商品ID列表
        List<Long> goodsIds = goodsList.stream()
                .map(HtGoods::getId)
                .collect(Collectors.toList());
        // 查询今日订单列表（只查询相关商品的订单）
        HtOrder orderQuery = new HtOrder();
        orderQuery.setCreatedAt(new Date());
        orderQuery.setGoodsIds(goodsIds);
        List<HtOrderSystemVo> orderList = htOrderMapper.selectHtOrderListAndSysUser(orderQuery);
        // 构建商品ID到用户名的映射
        Map<Integer, String> goodsIdToUserName = orderList.stream()
                .filter(order -> order.getGoodsId() != null && order.getRealName() != null)
                .collect(Collectors.toMap(
                        HtOrderSystemVo::getGoodsId,
                        HtOrderSystemVo::getRealName,
                        (existing, replacement) -> existing
                ));
        // 转换为HtGoodsItem并设置用户名
        return goodsList.stream()
                .map(goods -> {
                    HtGoodsItem item = new HtGoodsItem();
                    BeanUtils.copyProperties(goods, item);
                    item.setUserName(goodsIdToUserName.get(goods.getId().intValue()));
                    return item;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<AppUser> grabUser() {
        HtOrder orderQuery = new HtOrder();
        orderQuery.setCreatedAt(new Date());
        List<HtOrderSystemVo> orderList = htOrderMapper.selectHtOrderListAndSysUser(orderQuery);
        if (orderList.size() == goodsMapper.selectCount(Wrappers.<HtGoods>query().ne("id", 0).eq("is_best", 1).eq("is_new", 1).eq("is_del", 0))) {
            return null;
        } else {
            List<AppUser> appUsers = appUserMapper.selectList(null);
            // 获取已下单用户的ID列表
            List<Integer> orderedUserIds = orderList.stream()
                    .map(HtOrderSystemVo::getUserId)
                    .collect(Collectors.toList());

            // 过滤出未下单的用户
            return appUsers.stream()
                    .filter(user -> !orderedUserIds.contains(user.getId().intValue()))
                    .collect(Collectors.toList());
        }
    }
}


