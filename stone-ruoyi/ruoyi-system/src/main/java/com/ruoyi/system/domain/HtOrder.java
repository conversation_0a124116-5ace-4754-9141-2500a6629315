package com.ruoyi.system.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

//如果自己的包名修改了，则需要改成对应的包名
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 订单对象 ht_order
 *
 * <AUTHOR>
 * @date 2024-11-23
 */
@Data
@TableName("ht_order")
public class HtOrder implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 收款人主键
     */
    private Integer recipientId;
    /**
     * 收款人姓名
     */
    @Excel(name = "收款人姓名")
    private String recipientName;
    /**
     * 收款人联系电话
     */
    @Excel(name = "收款人联系电话")
    private String recipientPhone;
    /**
     * 付款金额
     */
    @Excel(name = "付款金额")
    private BigDecimal amount;
    /**
     * 商品金额
     */
    private BigDecimal goodsPrice;
    /**
     * 匹配时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "匹配时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date matchedTime;
    /**
     * 付款时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "付款时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date paymentTime;
    /**
     * 开户银行
     */
    @Excel(name = "开户银行")
    private String bankName;
    /**
     * 银行卡号
     */
    @Excel(name = "银行卡号")
    private String bankAccount;
    /**
     * 微信收款二维码图片地址
     */
    @Excel(name = "微信收款二维码图片地址")
    private String wechatQrCode;
    /**
     * 支付宝收款二维码图片地址
     */
    @Excel(name = "支付宝收款二维码图片地址")
    private String alipayQrCode;
    /**
     * 付款凭证图片地址
     */
    @Excel(name = "付款凭证图片地址")
    private String paymentProof;
    /**
     * 订单状态
     * 1 订单创建成功 待审核
     * 2 订单分配完成 待完成订单
     * 3 订单已支付   待确认收款
     * 4 订单已确认收款   待委托上架
     * 5 订单委托上架成功  待上架
     * 6 订单上架成功
     */
    @Excel(name = "订单状态")
    private Long orderStatus;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createdAt;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updatedAt;
    /**
     * 购买用户主键
     */
    @Excel(name = "购买用户主键")
    private Long userId;
    /**
     * 购买用户姓名
     */
    @TableField(exist = false)
    private String userName;
    /**
     * 商品id
     */
    @Excel(name = "商品id")
    private Long goodsId;
    /**
     * 订单编号
     */
    @Excel(name = "订单编号")
    private String orderId;

    @TableField(exist = false)
    private HtGoods htGoods;

    @TableField(exist = false)
    private List<PaymentProof> paymentProofList;

    @TableField(exist = false)
    private List<Long> goodsIds;
}

