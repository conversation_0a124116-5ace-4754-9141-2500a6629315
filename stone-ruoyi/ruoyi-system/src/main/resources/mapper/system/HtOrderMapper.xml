<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.HtOrderMapper">

    <resultMap type="HtOrder" id="HtOrderResult">
            <result property="id" column="id"/>
            <result property="recipientId" column="recipient_id"/>
            <result property="recipientName" column="recipient_name"/>
            <result property="recipientPhone" column="recipient_phone"/>
            <result property="amount" column="amount"/>
            <result property="matchedTime" column="matched_time"/>
            <result property="paymentTime" column="payment_time"/>
            <result property="goodsPrice" column="goods_price"/>
            <result property="bankName" column="bank_name"/>
            <result property="bankAccount" column="bank_account"/>
            <result property="wechatQrCode" column="wechat_qr_code"/>
            <result property="alipayQrCode" column="alipay_qr_code"/>
            <result property="paymentProof" column="payment_proof"/>
            <result property="orderStatus" column="order_status"/>
            <result property="createdAt" column="created_at"/>
            <result property="updatedAt" column="updated_at"/>
            <result property="userId" column="user_id"/>
            <result property="goodsId" column="goods_id"/>
            <result property="orderId" column="order_id"/>
    </resultMap>
    <resultMap type="com.ruoyi.system.domain.HtOrder" id="oldMap">
        <result column="matched_time"  property="matchedTime" />
        <result column="id"  property="id" />
        <result column="order_id"  property="orderId" />
        <result column="recipient_name"  property="recipientName" />
        <result column="recipient_phone"  property="recipientPhone" />
        <result column="amount"  property="amount" />
        <result column="order_status"  property="orderStatus" />
    </resultMap>

    <sql id="selectHtOrderVo">
        select id, recipient_name, recipient_id, recipient_phone, amount, matched_time, goods_price, payment_time, bank_name, bank_account, wechat_qr_code, alipay_qr_code, payment_proof, order_status, created_at, updated_at, user_id, goods_id, order_id, order_type
        from ht_order
    </sql>

    <select id="selectHtOrderList" parameterType="HtOrder" resultMap="HtOrderResult">
        <include refid="selectHtOrderVo"/>
        <where>
                        <if test="recipientName != null  and recipientName != ''">
                            and recipient_name like concat('%', #{recipientName}, '%')
                        </if>
                        <if test="recipientPhone != null  and recipientPhone != ''">
                            and recipient_phone = #{recipientPhone}
                        </if>
                        <if test="amount != null ">
                            and amount = #{amount}
                        </if>
                        <if test="matchedTime != null ">
                            and matched_time = #{matchedTime}
                        </if>
                        <if test="paymentTime != null ">
                            and payment_time = #{paymentTime}
                        </if>
                        <if test="bankName != null  and bankName != ''">
                            and bank_name like concat('%', #{bankName}, '%')
                        </if>
                        <if test="bankAccount != null  and bankAccount != ''">
                            and bank_account = #{bankAccount}
                        </if>
                        <if test="wechatQrCode != null  and wechatQrCode != ''">
                            and wechat_qr_code = #{wechatQrCode}
                        </if>
                        <if test="alipayQrCode != null  and alipayQrCode != ''">
                            and alipay_qr_code = #{alipayQrCode}
                        </if>
                        <if test="paymentProof != null  and paymentProof != ''">
                            and payment_proof = #{paymentProof}
                        </if>
                        <if test="orderStatus != null ">
                            and order_status = #{orderStatus}
                        </if>
                        <if test="createdAt != null ">
                            and created_at = #{createdAt}
                        </if>
                        <if test="updatedAt != null ">
                            and updated_at = #{updatedAt}
                        </if>
                        <if test="userId != null ">
                            and user_id = #{userId}
                        </if>
                        <if test="goodsId != null ">
                            and goods_id = #{goodsId}
                        </if>
                        <if test="orderId != null  and orderId != ''">
                            and order_id = #{orderId}
                        </if>


        </where>
        order by created_at desc
    </select>

    <select id="selectHtOrderById" parameterType="Long"
            resultMap="HtOrderResult">
            <include refid="selectHtOrderVo"/>
            where id = #{id}
    </select>

    <select id="selectByGoodIdLast" parameterType="Long" resultMap="HtOrderResult">
        <include refid="selectHtOrderVo"/>
        where goods_id = #{goodsId} order by created_at desc limit 1
    </select>

    <select id="selectOldList" parameterType="Long" resultMap="oldMap">
        select id , order_id ,recipient_name , recipient_phone, goods_pirce, order_status, order_type, DATE_FORMAT(matched_time, '%Y-%m-%d') as matched_time
        from ht_order where user_id  = #{id} and DATE(matched_time) &lt; CURDATE()   order by matched_time desc ;
    </select>

    <select id="selectSysHtOrderList"  parameterType="HtOrder" resultType="com.ruoyi.system.domain.vo.HtOrderSystemVo">
        SELECT hor.*, ho.order_status as orderStatus, au.real_name as realName, ho.goods_id as goodsId
        FROM ht_order_record hor
        LEFT JOIN ht_order ho
        ON ho.id = hor.order_id
        LEFT JOIN app_user au
        ON au.id = hor.user_id
        <where>
            <if test="orderStatus != null ">
                and ho.order_status = #{orderStatus}
            </if>
            <if test="createdAt != null">
                and DATE_FORMAT(ho.created_at, '%Y-%m-%d') = DATE_FORMAT(CURDATE(), '%Y-%m-%d')
            </if>
        </where>
        order by ho.created_at desc
    </select>
    <select id="selectHtOrderListAndSysUser" resultType="com.ruoyi.system.domain.vo.HtOrderSystemVo">
        SELECT ho.order_status as orderStatus, au.real_name as realName, ho.goods_id as goodsId,ho.user_id
        FROM ht_order ho
        LEFT JOIN app_user au
        ON au.id = ho.user_id
        <where>
            <if test="goodsIds != null and goodsIds.size() > 0">
                and ho.goods_id in
                <foreach collection="goodsIds" item="goodsId" open="(" separator="," close=")">
                    #{goodsId}
                </foreach>
            </if>
            <if test="createdAt != null">
                and DATE_FORMAT(ho.created_at, '%Y-%m-%d') = DATE_FORMAT(CURDATE(), '%Y-%m-%d')
            </if>
        </where>
        order by ho.created_at desc
    </select>

    <insert id="insertHtOrder" parameterType="HtOrder" useGeneratedKeys="true"
            keyProperty="id">
        insert into ht_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="recipientId != null and recipientId != ''">recipient_id,
            </if>
            <if test="recipientName != null and recipientName != ''">recipient_name,
            </if>
            <if test="recipientPhone != null and recipientPhone != ''">recipient_phone,
            </if>
            <if test="amount != null">amount,
            </if>
            <if test="goodsPrice != null">goods_price,
            </if>
            <if test="matchedTime != null">matched_time,
            </if>
            <if test="paymentTime != null">payment_time,
            </if>
            <if test="bankName != null">bank_name,
            </if>
            <if test="bankAccount != null">bank_account,
            </if>
            <if test="wechatQrCode != null">wechat_qr_code,
            </if>
            <if test="alipayQrCode != null">alipay_qr_code,
            </if>
            <if test="paymentProof != null">payment_proof,
            </if>
            <if test="orderStatus != null">order_status,
            </if>
            <if test="createdAt != null">created_at,
            </if>
            <if test="updatedAt != null">updated_at,
            </if>
            <if test="userId != null">user_id,
            </if>
            <if test="goodsId != null">goods_id,
            </if>
            <if test="orderId != null">order_id,
            </if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="recipientId != null and recipientId != ''">#{recipientId},
            </if>
            <if test="recipientName != null and recipientName != ''">#{recipientName},
            </if>
            <if test="recipientPhone != null and recipientPhone != ''">#{recipientPhone},
            </if>
            <if test="amount != null">#{amount},
            </if>
            <if test="goodsPrice != null">#{goodsPrice},
            </if>
            <if test="matchedTime != null">#{matchedTime},
            </if>
            <if test="paymentTime != null">#{paymentTime},
            </if>
            <if test="bankName != null">#{bankName},
            </if>
            <if test="bankAccount != null">#{bankAccount},
            </if>
            <if test="wechatQrCode != null">#{wechatQrCode},
            </if>
            <if test="alipayQrCode != null">#{alipayQrCode},
            </if>
            <if test="paymentProof != null">#{paymentProof},
            </if>
            <if test="orderStatus != null">#{orderStatus},
            </if>
            <if test="createdAt != null">#{createdAt},
            </if>
            <if test="updatedAt != null">#{updatedAt},
            </if>
            <if test="userId != null">#{userId},
            </if>
            <if test="goodsId != null">#{goodsId},
            </if>
            <if test="orderId != null">#{orderId},
            </if>

        </trim>
    </insert>

    <update id="updateHtOrder" parameterType="HtOrder">
        update ht_order
        <trim prefix="SET" suffixOverrides=",">
                    <if test="recipientId != null and recipientId != ''">recipient_id = #{recipientId},
                    </if>
                    <if test="recipientName != null and recipientName != ''">recipient_name =
                        #{recipientName},
                    </if>
                    <if test="recipientPhone != null and recipientPhone != ''">recipient_phone =
                        #{recipientPhone},
                    </if>
                    <if test="amount != null">amount =
                        #{amount},
                    </if>
                    <if test="goodsPrice != null">goods_price =
                        #{goodsPrice},
                    </if>
                    <if test="matchedTime != null">matched_time =
                        #{matchedTime},
                    </if>
                    <if test="paymentTime != null">payment_time =
                        #{paymentTime},
                    </if>
                    <if test="bankName != null">bank_name =
                        #{bankName},
                    </if>
                    <if test="bankAccount != null">bank_account =
                        #{bankAccount},
                    </if>
                    <if test="wechatQrCode != null">wechat_qr_code =
                        #{wechatQrCode},
                    </if>
                    <if test="alipayQrCode != null">alipay_qr_code =
                        #{alipayQrCode},
                    </if>
                    <if test="paymentProof != null">payment_proof =
                        #{paymentProof},
                    </if>
                    <if test="orderStatus != null">order_status =
                        #{orderStatus},
                    </if>
                    <if test="createdAt != null">created_at =
                        #{createdAt},
                    </if>
                    <if test="updatedAt != null">updated_at =
                        #{updatedAt},
                    </if>
                    <if test="userId != null">user_id =
                        #{userId},
                    </if>
                    <if test="goodsId != null">goods_id =
                        #{goodsId},
                    </if>
                    <if test="orderId != null">order_id =
                        #{orderId},
                    </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHtOrderById" parameterType="Long">
        delete
        from ht_order where id = #{id}
    </delete>

    <delete id="deleteHtOrderByIds" parameterType="String">
        delete from ht_order where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>