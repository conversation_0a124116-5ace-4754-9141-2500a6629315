package com.ruoyi.api.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.HtGoods;
import com.ruoyi.system.service.IHtGoodsService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 商品Controller
 *
 * <AUTHOR>
 * @date 2024-11-21
 */
@RestController
@RequestMapping("/api/system/goods")
public class ApiHtGoodsController extends BaseController {

    @Autowired
    private IHtGoodsService htGoodsService;

    @Autowired
    private RedisCache redisCache;
    /**
     * 查询商品列表
     */
    //@PreAuthorize("@ss.hasPermi('system:goods:list')")
    @GetMapping("/list")
    public TableDataInfo list(HtGoods htGoods) {
        startPage();
        List<HtGoods> list = htGoodsService.selectHtGoodsList(htGoods);
        return getDataTable(list);
    }
    /**
     * 查询我的商品列表
     */
    @GetMapping("/myList/{userId}")
    public TableDataInfo myList(@PathVariable("userId") Long userId) {
        startPage();
        List<HtGoods> list = htGoodsService.selectMyHtGoodsList(userId);
        return getDataTable(list);
    }
    /**
     * 导出商品列表
     */
//    @PreAuthorize("@ss.hasPermi('system:goods:export')")
    @Log(title = "商品", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HtGoods htGoods) {
        List<HtGoods> list = htGoodsService.selectHtGoodsList(htGoods);
        ExcelUtil<HtGoods> util = new ExcelUtil<HtGoods>(HtGoods.class);
        util.exportExcel(response, list, "商品数据");
    }

    /**
     * 获取商品详细信息
     */
//    @PreAuthorize("@ss.hasPermi('system:goods:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(htGoodsService.selectHtGoodsById(id));
    }

    /**
     * 新增商品
     */
//    @PreAuthorize("@ss.hasPermi('system:goods:add')")
    @Log(title = "商品", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HtGoods htGoods) {
        return toAjax(htGoodsService.insertHtGoods(htGoods));
    }

    /**
     * 修改商品
     */
//    @PreAuthorize("@ss.hasPermi('system:goods:edit')")
    @Log(title = "商品", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HtGoods htGoods) {
        return toAjax(htGoodsService.updateHtGoods(htGoods));
    }

    /**
     * 删除商品
     */
//    @PreAuthorize("@ss.hasPermi('system:goods:remove')")
    @Log(title = "商品", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(htGoodsService.deleteHtGoodsByIds(ids));
    }

    /**
     * 抢购
     *
     * @param userId
     * @param goodsId
     * @return
     */
    @GetMapping("/{userId}/{goodsId}")
    public AjaxResult snappedGoods(@PathVariable(value = "userId") Integer userId,
                                   @PathVariable(value = "goodsId") Integer goodsId) {
        return  htGoodsService.snappedGoodsOptimized(userId, goodsId);
    }
}
