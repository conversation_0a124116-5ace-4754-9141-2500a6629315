<template>
	<view class="container">
		<!-- 顶部标题 -->
		<u-sticky>
			<view class="count-down-box">
				距离抢购结束：
				<u-count-down :time="countDown*1000" format="HH:mm:ss:SSS" autoStart millisecond
					@finish="updateSoldOutStatus">
				</u-count-down>
			</view>
		</u-sticky>
		<!-- 筛选条件 -->
		<view class="filter-bar">
			<button :class="{ active: sortCondition === 'default' }" @click="changeSort('default')">默认排序</button>
			<button :class="{ active: sortCondition === 'asc' }" @click="changeSort('asc')">价格 ↑</button>
			<button :class="{ active: sortCondition === 'desc' }" @click="changeSort('desc')">价格 ↓</button>
		</view>


		<!-- 商品列表 -->
		<view class="product-list">
			<view class="product-item" v-for="(product, index) in filteredProducts" :key="index"
				@click="onProductClick(product)">
				<image :src="product.image" class="product-image" />
				<view class="product-info">
					<text class="product-name">{{ product.storeName }}</text>
					<text class="product-price">¥ {{ product.price }}</text>
					<!-- <view v-if="product.isDel" class="sold-out">已售罄</view> -->
				</view>
			</view>
		</view>

		<!-- 分页 -->
		<!-- <u-loadmore v-if="total>10" :status="loadMoreStatus" /> -->
		<view class="pagination">
			<button @click="prevPage" :disabled="currentPage === 1">上一页</button>
			<view
				style="white-space: nowrap; overflow: hidden;text-overflow: ellipsis;width: 300rpx;display: flex;flex-direction: row;justify-content: space-around;">
				<text v-for="(page,j) in totalPages" :key="page" :class="{ active: page === currentPage }"
					@click="goToPage(j)">
					{{ page }}
				</text>
			</view>
			<button @click="nextPage" :disabled="currentPage === totalPages">下一页</button>
		</view>
	</view>
</template>

<script>
	import list from '../../uni_modules/uview-ui/libs/config/props/list';

	export default {
		data() {
			return {
				id: '',
				activityId: '',
				products: [], // 所有商品
				filteredProducts: [], // 当前显示的商品
				currentPage: 1, // 当前页码
				total: 0,
				totalPages: 5, // 总页数
				currentSize: 10, // 总展示
				sortCondition: 'default', // 当前排序条件
				countDown: 0, // 倒计时
				countDownData: {},
				loadMoreStatus: 'loadmore',
				disflag: false,
			};
		},
		created() {
			this.loadProducts();
			this.startCountDown();
		},
		onLoad(options) {
			this.id = options.id;
			this.activityId = options.id
			this.api_get(options.id)
		},
		methods: {
			api_get(id) {
				console.log(id, 'id');
				let userInfo = uni.getStorageSync('userInfo')
				this.$api.request({
					url: this.$api.getrecommendlist + '?activityId=' + id + '&pageNum=' + this.currentPage +
						'&pageSize=' + this.currentSize + '&userId=' + userInfo.id
				}).then((res) => {
					// this.filteredProducts = this.filteredProducts.concat(res.rows)
					if (res.code == 200) {
						if (res.rows.length != 0) {
							this.filteredProducts = res.rows
							this.total = res.total;
							this.totalPages = Math.ceil(res.total / this.currentSize)
						} else {
							
						}
					}
					// uni.stopPullDownRefresh();
				})
			},
			// 加载商品数据
			loadProducts() {
				this.applySort();
			},
			// 根据排序条件过滤商品
			applySort() {
				let sortedProducts = [...this.filteredProducts];
				let userInfo = uni.getStorageSync('userInfo')
				if (this.sortCondition === 'asc') {
					this.$api.request({
						url: this.$api.getrecommendlist + '?activityId=' + this.activityId + '&type=0' +
							'&pageNumer=' + this.currentPage +
							'&pageSize=' + this.currentSize + '&userId=' + userInfo.id
					}).then((res) => {
						console.log(res);
						this.filteredProducts = res.rows
					})
				} else if (this.sortCondition === 'desc') {
					this.$api.request({
						url: this.$api.getrecommendlist + '?activityId=' + this.activityId + '&type=1' +
							'&pageNumer=' + this.currentPage +
							'&pageSize=' + this.currentSize + '&userId=' + userInfo.id
					}).then((res) => {
						console.log(res);
						this.filteredProducts = res.rows
					})
				}
				const start = (this.currentPage - 1) * this.currentSize;
				const end = this.currentPage * this.currentSize;
				this.filteredProducts = sortedProducts.slice(start, end);
			},
			// 修改排序条件
			changeSort(condition) {
				this.sortCondition = condition;
				this.applySort();
			},
			// 商品点击事件
			onProductClick(product) {
				let userInfo = uni.getStorageSync('userInfo')
				console.log(product);
				if (product.isSoldOut) {
					uni.showToast({
						title: '商品已售罄',
						icon: 'none'
					});
				} else {
					let holder = {}
					this.$api.request({
						url: this.$api.getpayuserinfo + '/' + product.userId,
						method: "get",
					}).then(res => {
						if (res.code == 200) {
							holder = res.data

							uni.showModal({
								title: '是否抢购该商品',
								content: '持有者:' + holder.realName + `\n` + '电话:' + holder.phone,
								success: (res) => {
									if (res.confirm) {
										this.$api.request({
											url: this.$api.getdetial + '/' + userInfo.id +
												'/' + product.id
										}).then((res) => {
											if (res.code == 200){
												uni.showToast({
													title: '抢购成功'
												})
												this.filteredProducts = []
												this.api_get(this.id)
												// uni.navigateBack()
												setTimeout(() => {
													uni.switchTab({
														url: '/pages/seckill/list'
													})
												}, 1500)
											} else{
												uni.showToast({
													icon: 'error',
													title: res.data.msg,
												})
											}
										})
									}
								}
							})
						}
					})

				}
			},
			// 分页功能
			prevPage() {
				if (this.currentPage > 0) {
					this.currentPage--;
					this.api_get(this.activityId);
				}
			},
			nextPage() {
				if (this.currentPage < this.totalPages) {
					this.currentPage++;
					this.api_get(this.activityId);
				}
			},
			goToPage(page) {
				this.currentPage = page + 1;
				console.log(this.currentPage);
				this.api_get(this.activityId)
				// this.applySort();
			},
			// 刷新页面
			refreshPage() {
				this.loadProducts();
			},
			// 倒计时功能
			startCountDown() {
				this.countDown = 20 * 60; // 总秒数
			},
			// 更新已售罄状态
			updateSoldOutStatus() {
				this.products.forEach((product) => (product.isSoldOut = true));
				this.applySort();
			},
		},
	};
</script>

<style scoped lang="scss">
	.container {
		display: flex;
		flex-direction: column;
		padding: 10rpx;
	}

	.count-down-box {
		background-color: #FFF;
		display: flex;
		justify-content: center;


	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 10rpx 20rpx;
		background-color: #3cb371;
		color: white;
	}

	.title {
		font-size: 36rpx;
	}

	.refresh-btn {
		background-color: white;
		color: #3cb371;
		padding: 5rpx 10rpx;
		border-radius: 5rpx;
	}

	.filter-bar {
		display: flex;
		justify-content: space-around;
		margin: 10rpx 0;
	}

	.filter-bar button {
		border: none;
		background-color: white;
		color: #333;
		padding: 5rpx 20rpx;
		border-radius: 5rpx;
		font-size: 24rpx;
	}

	.filter-bar .active {
		color: #3cb371;
		font-weight: bold;
	}

	.product-list {
		display: flex;
		flex-wrap: wrap;
	}

	.product-item {
		width: 47%;
		margin: 1%;
		background-color: white;
		border: 1rpx solid #ddd;
		border-radius: 5rpx;
		overflow: hidden;
		position: relative;
		display: inline-block;
	}

	.product-image {
		width: 100%;
		height: 200rpx;
	}

	.product-info {
		padding: 10rpx;
	}

	.product-name {
		font-size: 26rpx;
		color: #333;
	}

	.product-price {
		font-size: 28rpx;
		color: #e63946;
	}

	.sold-out {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(255, 0, 0, 0.5);
		color: white;
		font-size: 36rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.pagination {
		display: flex;
		justify-content: center;
		margin: 20rpx 0;
	}

	.pagination button {
		background-color: #3cb371;
		color: white;
		border: none;
		border-radius: 5rpx;
		font-size: 18upx;
	}

	.pagination text {
		margin: 0 10rpx;
		font-size: 24rpx;
		cursor: pointer;
	}

	.pagination .active {
		background-color: green;
		line-height: 40rpx;
		padding: 0 10rpx;
		color: #fff;
		font-weight: bold;
	}
</style>