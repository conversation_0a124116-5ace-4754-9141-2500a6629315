<template>
	<view class="container">
		<!-- Tab 切换 -->
		<view class="tabs">
			<text :class="{ active: currentTab === 'buyer' }" @click="switchTab('buyer')">买方</text>
			<text :class="{ active: currentTab === 'seller' }" @click="switchTab('seller')">卖方</text>
		</view>

		<!-- 订单列表 -->
		<view v-if="currentTab === 'buyer'" class="order-list">
			<view v-if="buyerOrders.length!=0">
				<view v-for="order in buyerOrders" :key="order.id" class="order-item">
					<image :src="order.image" class="order-image" />
					<view class="order-info">
						<view class="order-name">商品：{{ order.storeName }}</view>
						<view class="order-price">价格：¥ {{ order.price }}</view>
						<view class="order-status">{{ order.status }}</view>
					</view>
					<button class="detail-btn" disabled="true" v-if="order.orderStatus=='1'">待审核</button>
					<button class="detail-btn" @click="goPayDetail(order)" v-if="order.orderStatus=='2'">待完成订单</button>
					<button class="detail-btn" disabled="true" v-if="order.orderStatus=='3'">待确认收款</button>
					<button v-if="order.orderStatus=='4'" class="detail-btn" @click="viewOrder(order)">
						委托寄售
					</button>
				</view>
			</view>
			<view v-else>
				<img src="../../static/goodsnone.png" alt="" style="display: block;margin: 0 auto;">
			</view>
		</view>

		<view v-else class="order-list">
			<view v-for="order in sellerOrders" :key="order.id" class="order-item">
				<image :src="order.image" class="order-image" />
				<view class="order-info">
					<view class="order-name">商品：{{ order.storeName }}</view>
					<view v-if="order.orderStatus==5" class="order-price">价格：¥
						{{ (Number(order.price) * 1.05).toFixed(2) }}
					</view>
					<view v-if="order.orderStatus==6" class="order-price">价格：¥ {{ order.price }}</view>
					<view class="order-price">下单时间：{{ order.createdAt }}</view>
				</view>
				<button v-if="order.orderStatus==5" class="detail-btn">
					委托寄售成功
				</button>
				<button v-if="order.orderStatus==6" class="detail-btn">
					上架成功
				</button>
				<!-- <button v-if="order.orderStatus==9" class="detail-btn" >
					确认收款
				</button> -->
			</view>
		</view>
		<button class=" warehousebtn" @click="warehouseDetial">收支详情</button>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				currentTab: "buyer", // 当前选项卡：买方或卖方
				buyerOrders: [],
				sellerOrders: [],
			};
		},
		onLoad() {
			this.api_get()
			this.api_get_2()
		},
		onShow() {
			this.api_get()
			this.api_get_2()
		},
		methods: {
			api_get() {
				let userId = uni.getStorageSync('userInfo')
				this.$api.request({
					url: this.$api.getwarehouse + `/${userId.id}`
				}).then((res) => {
					console.log(res);
					this.buyerOrders = res.data
				})
			},
			api_get_2() {
				let userId = uni.getStorageSync('userInfo')
				this.$api.request({
					url: this.$api.getwarehouse_2 + `/${userId.id}`
				}).then((res) => {
					console.log(res, '22222222');
					this.sellerOrders = res.data
				})
			},

			switchTab(tab) {
				this.currentTab = tab;
				if (this.currentTab == 'buyer') {
					this.api_get()
				} else {
					this.api_get_2()
				}
			},
			getConfirm(orderId) {
				uni.showModal({
					title: '操作通知',
					content: '是否确认本次收款',
					success: (res) => {
						if (res.confirm) {
							this.$api.request({
								url: this.$api.getmoneyComfirm + '/' + orderId
							}).then((res) => {
								console.log(res);
								if (res.code == 200) {
									uni.showToast({
										title: '收款成功'
									})
									setTimeout(() => {
										this.api_get_2()
									}, 1200)
								}
							})
						}
					}
				})
			},
			refreshPage() {
				console.log("刷新页面");
			},
			viewOrder(order) {
				uni.navigateTo({
					url: `/pages/order/orderDetail?id=${order.orderId}&storename=${order.storeName}&price=${order.price}`
				});
			},
			goPayDetail(order) {
				console.log(order, 'order');
				uni.navigateTo({
					url: `/pages/order/payDetail?id=${order.orderId}`
				});
			},
			warehouseDetial() {
				console.log('1111');
				uni.navigateTo({
					url: '/pages/myPage/sec_page/todaymoney'
				})
			},
		},
	};
</script>

<style>
	.container {
		padding: 20rpx;
		background-color: #f5f5f5;
	}

	.header {
		display: flex;
		justify-content: space-between;
		background-color: #3cb371;
		padding: 10rpx 20rpx;
		color: white;
	}

	.tabs {
		display: flex;
		justify-content: space-around;
		background-color: white;
		margin-top: 20rpx;
	}

	.tabs text {
		margin: 0 10rpx;
		padding: 10rpx 4vh;
		color: #666;
	}

	.tabs .active {
		/* color: #3cb371; */
		/* border-bottom: 2rpx solid #3cb371; */
		font-weight: bold;
		background-color: red;
		color: white;
		border-radius: 2vh;

	}

	.order-list {
		margin-top: 20rpx;
	}

	.order-item {
		display: flex;
		justify-content: space-between;
		padding: 10rpx;
		background-color: white;
		margin-bottom: 10rpx;
		border-radius: 10rpx;
	}

	.order-image {
		width: 150rpx;
		height: 150rpx;
	}

	.order-info {
		flex: 1;
		padding: 0 10rpx;
	}

	.order-name,
	.order-price {
		line-height: 40rpx;
		font-size: 24rpx;
		margin-bottom: 5rpx;
	}

	.detail-btn {
		background-color: #3cb371;
		color: white;
		padding: 10rpx 20rpx;
		border-radius: 10rpx;
		font-size: 26rpx;
		display: flex;
		align-items: center;
	}

	.warehousebtn {
		background-color: #3cb371;
		color: #fff;
		width: 30vw;
		position: fixed;
		bottom: 10%;
		right: 4vw;
	}
</style>