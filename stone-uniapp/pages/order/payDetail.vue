<template>
	<view class="container">
		<view class="top">
			<view >
				<view v-for="(item,index) in obj" :key="index">
					<view class="list-item">
						<view class="left">
							收款人姓名：
						</view>
						<view class="right">
							<u--text type="warning" :text="item.recipientName"></u--text>
							<u-button class="copy-button" size="small" text="复制"
								@click="copyText(item.recipientName)"></u-button>
						</view>
					</view>
					<view class="list-item">
						<view class="left">
							联系电话：
						</view>
						<view class="right">
							{{item.recipientPhone}}
						</view>
					</view>
					<view class="list-item">
						<view class="left">
							付款金额：
						</view>
						<view class="right">
							￥<u--text type="error" :text="item.amount" size="30"></u--text>
						</view>
					</view>
					<view class="bottom">
						<view class="title">
							收款方式
						</view>
						<view class="list-item">
							<view class="left">
								开户银行：
							</view>
							<view class="right">
								{{item.bank.bankName}}
							</view>
						</view>
						<view class="list-item">
							<view class=" left">
								银行卡号：
							</view>
							<view class="right">
								<u--text type="warning" :text="item.bank.accountInfo"></u--text>
								<u-button class="copy-button" size="small" text="复制"
									@click="copyText(item.bank.accountInfo)"></u-button>
							</view>
						</view>
						<view class="list-item">
							<view class="left">
								微信收款码：
							</view>
							<view class="right">
								<u--image :src="item.wechat.chatImg==null?'':item.wechat.chatImg" width="80px"
									height="80px" @click="previewImage(item.wechat.chatImg)"></u--image>
							</view>
						</view>
						<view class="list-item">
							<view class="left">
								支付宝收款码：
							</view>
							<view class="right">
								<u--image :src="item.pay.alipayImg==null?'':item.pay.alipayImg" width="80px"
									height="80px" @click="previewImage(item.pay.alipayImg)"></u--image>
							</view>
						</view>

						<view class="list-item">
							<view class="left">
								付款凭证：
							</view>
							<view class="right">
								<u-upload :fileList="item.fileList" :name="''+index" @afterRead="afterRead"
									@delete="deletePic" multiple :key="'upload'+index"></u-upload>
							</view>
						</view>
					</view>
				</view>

				<view>
					<button @click="payDetailSubmit">提交</button>
				</view>
			</view>
			<!-- <view v-else-if="isPay==false">
				<view v-for="(k,j) in obj">
					<view class="list-item">
						<view class="left">
							付款人名称：
						</view>
						<view class="right">
							<u--text type="warning" :text="k.userName"></u--text>
							<u-button class="copy-button" size="small" text="复制" @click="copyText(k.userId)"></u-button>
						</view>
					</view>
					<view class="list-item">
						<view class="left">
							联系电话：
						</view>
						<view class="right">
							{{k.userPhone}}
						</view>
					</view>
					<view class="list-item">
						<view class="left">
							应收款金额：
						</view>
						<view class="right">
							￥<u--text type="error" :text="k.amount" size="30"></u--text>
						</view>
					</view>

				</view>
				<view style="font-weight: bold;position: fixed;bottom: 40rpx;font-size: 50rpx;">共收款金额：<text
						style="color: red;">￥{{all}}</text></view> -->
			</view>

		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				userId: '',
				goodId: '',
				obj: [],
				price: 0,
				all: 0,
			}
		},
		onLoad(options) {
			console.log(options.id, '接到的数据');
			this.goodId = options.id
			let userInfo = uni.getStorageSync('userInfo')
			console.log(userInfo.id, '当前用户id');
			this.api_get_payway(userInfo.id)
		},
		methods: {
			copyText(text) {
				uni.setClipboardData({
					data: text,
					success: function() {
						uni.showToast({
							title: '手机号已复制',
							icon: 'none'
						})
					}
				});
			},
			previewImage(photoImg) {
				let imgsArray = [];
				imgsArray[0] = photoImg;
				console.log('imgsArray[0]', imgsArray[0])
				uni.previewImage({
					current: 0,
					urls: imgsArray,
				});
			},
			api_get_payway(id) {
				this.$api.request({
					url: this.$api.getdetaillist + `/` + id
				}).then((res) => {
					console.log(res.data, '获取的数据');
					
					let obj = res.data
					obj.forEach((item, index) => {
						obj[index].fileList = [];
					})
					obj.forEach((item, index) => {
						this.all += item.amount
					})
					this.all = this.all.toFixed(2)
					this.obj = obj;
				})
			},
			// 删除图片
			deletePic(event) {
				this.obj[event.name].fileList.splice(event.index, 1)
			},
			// 新增图片
			async afterRead(event) {
				// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
				let lists = [].concat(event.file)
				let fileListLen = this.obj[event.name].fileList.length
				let filePath
				lists.map((item) => {
					this.obj[event.name].fileList.push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				for (let i = 0; i < lists.length; i++) {
					let result = await this.$api.uploadFile({
						url: this.$api.upPic,
						filePath: lists[i].url
					});
					result = JSON.parse(result);
					let item = this.obj[event.name].fileList[fileListLen]
					this.obj[event.name].fileList.splice(fileListLen, 1, Object.assign(item, {
						status: 'success',
						message: '',
						url: result.url
					}))
					this.$forceUpdate();
					fileListLen++
				}
			},
			payDetailSubmit() {
				let checkPass = true;
				this.obj.forEach((item, index) => {
					if (item.fileList.length == 0) {
						uni.showToast({
							title: '请上传凭证',
							icon: "none",
						})
						checkPass = false;
					}
				})
				if (!checkPass) {
					return;
				}
				console.log(this.obj);
				let htOrderList = [{
					orderRecordId: "",
					image: []
				}]
				let a = []
				htOrderList = this.obj.map(order => ({
						orderRecordId: order.id, // 映射 order_id 为 orderId
						image: order.fileList.map(item => {
							return item.url;
						})
					})),
					console.log(htOrderList, '整合的数据');
				let htOrder = {
					id: this.goodId,
					paymentProofList: htOrderList
				}

				this.$api.request({
					url: this.$api.submitorder,
					method: "POST",
					data: htOrder,
				}).then((res) => {
					uni.showToast({
						title: '提交成功'
					})
					setTimeout(() => {
						uni.navigateBack()
					}, 2000)
				})
			}
		},
	}
</script>

<style lang="scss">
	.container {
		height: 100vh;
		background-color: #f5f5f5;
		padding: 30rpx;

		.copy-button {
			width: 30rpx;
		}

		.top,
		.bottom {
			background-color: #FFF;
			border-radius: 10rpx;
			padding: 10rpx;

			.list-item {
				padding: 0rpx 10rpx;
				min-height: 80rpx;
				display: flex;
				align-items: center;
				margin-bottom: 10rpx;

				.right {
					display: flex;
					align-items: center;
					flex: 1;
				}

				.right-span {
					margin-left: 10rpx;
				}
			}

			.title {
				display: flex;
				justify-content: center;
				align-items: center;
				font-size: 40rpx;
				font-weight: bold;
				height: 80rpx;
				background-color: #ccc;
			}
		}

		.top {
			margin-bottom: 20rpx;
		}
	}
</style>