<template>
	<view class="content">
		<view class="list">
			<view class="tabs">
				<view v-for="(item,index) in tabs" :key="index" :class="tabid==index?'on':''" @click="tabchange(index)">
					{{item}}
				</view>
			</view>
			<view class="list_item" v-for="(item,index) in beforeList">
				<view class="lt">
					<view>
						<text v-if="tabid==0">收款人：{{item.recipientName}}</text>
						<text v-else>付款人：{{item.payName}}</text>
					</view>
					<view>
						<text v-if="tabid==0">联系电话：{{item.recipientPhone}}</text>
						<text v-else>联系电话：{{item.payPhone}}</text>
					</view>
					<view>
						<text v-if="tabid==0">需付款金额：{{item.amount}} 元</text>
						<text v-else>需收款金额：{{item.amount}} 元</text>
					</view>
					<view v-if="item.paymentProof">
						凭证：
						<image :src="item.paymentProof" style="width: 100rpx;height: 100rpx;"
							@click="checkpic(item.paymentProof)" alt="">
					</view>
				</view>
				<view class="rt">
					<text v-if="flag==true">
						<b v-if="item.type==0 ">
							未确认收款
						</b>
						<b v-else-if="item.type==1">
							已确认收款
						</b>
					</text>
					<text v-else>
						<b v-if="item.type==1">已收款</b>
						<b v-else-if="item.type==0 && item.orderStatus==3" @click="getConfirm(item.id)">确认收款</b>
						<b v-else>未收款</b>
					</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				tabs: ['支出', '收入'],
				tabid: 0,
				flag: true,
				beforeList: []
			}
		},
		onLoad() {
			this.getapi()
		},
		methods: {
			tabchange(index) {
				this.tabid = index
				if (this.tabid == 0) {
					this.flag = true
					this.getapi()
				} else {
					this.flag = false
					this.getapi()
				}
			},
			checkpic(url) {
				uni.previewImage({
					urls: [url],
					// longPressActions: {
					// 	itemList: ['发送给朋友', '保存图片', '收藏'],
					// 	success: function(data) {
					// 		console.log('选中了第' + (data.tapIndex + 1) + '个按钮,第' + (data.index + 1) + '张图片');
					// 	},
					// 	fail: function(err) {
					// 		console.log(err.errMsg);
					// 	}
					// }
				});
			},
			getapi() {
				let appUser = uni.getStorageSync('userInfo')
				this.$api.request({
					url: this.$api.gettodaymoney + '/' + appUser.id
				}).then((res) => {
					if (this.flag == true) {
						this.beforeList = res.data.payMoney
					} else {
						this.beforeList = res.data.RevenueMoney
					}
				})
			},
			getConfirm(recordId) {
				uni.showModal({
					title: '操作通知',
					content: '是否确认本次收款',
					success: (res) => {
						if (res.confirm) {
							this.$api.request({
								url: this.$api.getmoneyComfirm + '/' + recordId
							}).then((res) => {
								console.log(res);
								if (res.code == 200) {
									uni.showToast({
										title: '收款成功'
									})
									setTimeout(() => {
										this.getapi()
									}, 1200)
								}
							})
						}
					}
				})
			},
		}
	}
</script>

<style scoped>
	.content {
		width: 100%;
	}

	.tabs {
		width: 100%;
		height: 120rpx;
		/* background-color: #ccc; */
		display: flex;
		flex-direction: row;
		justify-content: space-around
	}

	.tabs view {
		margin-top: 10rpx;
		width: 300rpx;
		height: 100rpx;
		background-color: brown;
		color: #fff;
		text-align: center;
		line-height: 100rpx;
		font-weight: bold;
	}

	.tabs view.on {
		background-color: red;
		color: #fff;
	}

	.list_item {
		width: 86vw;
		display: flex;
		justify-content: space-between;
		padding: 0 4vw;
		margin: 1vh auto;
		font-size: 30rpx;
		line-height: 4vh;
		font-weight: bold;
		border: 1rpx solid #000;
		box-shadow: 0px 2px 3px rgba(0, 0, 0, 0.2);
	}

	.lt {
		float: left;
	}

	.rt {
		float: right;
	}

	.rt text {
		display: inline-block;
		color: #fff;
		margin-top: 3vh;
		padding: 10rpx 20rpx;
		background-color: green;
	}
</style>