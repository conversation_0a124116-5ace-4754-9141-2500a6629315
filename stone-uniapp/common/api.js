import store from '@/store/index.js';
// import { routerJump } from '@/common/common.js'
//import store from "../store";
// const baseURL = "https://4iqmq3846460.vicp.fun"
const baseURL = "http://211.101.244.157:20001"
 //const baseURL = "http://127.0.0.1:20001"
 //const baseURL = "http://116.204.89.181:20001"
const loading = false
export default {
	info: "/api/login/info",
	getsignnam: '/api/get',
	// 石头

	// 登陆部分
	tologinbycp: '/api/login', // 登录接口
	getdecode: '/api/sendMsg', // 获取验证码
	register: '/api/register', // 用户注册
	register_2: '/api/referrerRegister', // 用户注册 有推荐人
	isConfirm: '/api/getConfirm', // 跳转首款管理确认 

	fixpwd: '/api/updatePassword', //修改密码
	fixpaypwd: '/api/transactionPassword', //修改交易密码
	getmoneyComfirm: '/api/order/confirmPayment', // 确认收款
	// 首页部分
	getswiper: '/api/system/banner/list', // 获取首页轮播图
	getrecommendlist: '/api/system/goods/list', //获取推荐/新品 列表  isBest/isNew
	isgoodempty: '/api/system/goods/delRedis', // 当商品为空时
	getdetial: '/api/system/goods', // 商品详情
	paygoods: '/system/order', //购买商品

	// 我的页面
	// 地址部份
	getaddresslist: '/api/address/list', // 获取地址列表信息
	deladdress: '/api/address', //删除地址
	updataaddress: '/api/address/updateAddress', //修改地址
	plusaddress: '/api/address/insertAddress', // 新增地址

	gettodaymoney: '/api/order/getTodayMoney', // 今日收支
	getyestodaybeforemyget: '/api/order/getPastMoney', // 往日收益
	getpayways: '/api/agreements/list', //签约的列表
	getmyshare: '/api/agreements/list', //我的分享
	delbankitem: '/api/agreements', // 删除银行item  delete
	addbankitem: '/api/agreements', // 新增银行item  post
	putbankitem: '/api/agreements', // 修改支付图片 put

	getmystonelist: '/api/system/goods/myList/',
	getteamlist: '/api/team/list', // 获取团队订单
	getdistribuction: '/api/distribution/list', // 获取分销列表

	getmysharelist: '/api/distribution/list', // 我的分享

	getwarehouse: '/api/warehouse/BuyerList', // 查询仓库状态
	getwarehouse_2: '/api/warehouse/SellerList', // 查询仓库状态
	getActlist: '/api/system/HtActivity/list', // 获取活动列表
	getpayuserinfo: '/api/user', // 获取用户付款方式
	postorderpay: '/api/order/pay', // 创建订单
	upPics: '/api/uploads',
	upPic: '/api/upload',
	upPic_2: baseURL + '/api/upload',
	saveSign: '/api/update', // 存签名

	getdetaillist: '/api/order/getPayInfo',
	gainsList: '/api/gains/gainsList', //我的收益
	getseildetial: '/api/order/detail',
	changeStatus: '/api/system/goods/updateStatus', // 修改商品状态
	submitorder: '/api/order/updateOrderType',
	putmessionon: '/api/system/audit/submit',
	getuserInfo: '/api/user',

	handleLogin(token, userId) {
		//返回token
		// store.dispatch('stateActions', {key: 'token', data:token})
		uni.setStorageSync('token', token)
		try {
			//将数据存放在以存在的key上,进行覆盖
			uni.setStorageSync('userId', parseInt(userId))
		} catch (e) {
			// error
			console.log(e)
		}
		// routerJump('/pages/index/index', {}, 'switchTab')

	},

	// 显示模态弹窗
	showModal(data) {
		let params = {
			title: data && data.title || '标题',
			content: data && data.content || '内容',
			showCancel: data && Boolean(data.showCancel) || true,
			cancelText: data && data.cancelText || '取消',
			cancelColor: data && data.cancelColor || '#000000',
			confirmText: data && data.confirmText || '确定',
			confirmColor: data && data.confirmColor || '#3CC51F',
		}
		return new Promise((resolve, reject) => {
			uni.showModal({
				title: params.title,
				content: params.content,
				showCancel: params.showCancel,
				cancelText: params.cancelText,
				cancelColor: params.cancelColor,
				confirmText: params.confirmText,
				confirmColor: params.confirmColor,
				success: (res) => {
					console.log(res)
					resolve(res)
				},
				fail: (err) => {
					reject(err)
				}
			})
		})
	},
	// 从底部向上弹出操作菜单
	showActionSheet(data) {
		let params = {
			itemList: data && data.itemList || [],
			itemColor: data && data.itemColor || '#000000',
			popover: data && data.popover || {},
		}
		return new Promise((resolve, reject) => {
			uni.showActionSheet({
				itemList: params.itemList,
				itemColor: params.itemColor,
				popover: params.popover,
				success: (res) => {
					console.log(res)
					resolve(res)
				},
				fail: (err) => {
					reject(err)
				}
			})
		})
	},
	// 选择图片
	chooseImage(data) {
		let params = {
			count: data && data.count || 9,
			sizeType: data && data.sizeType || ['original', 'compressed'],
			extension: data && data.extension || '',
			sourceType: data && data.filePath || ['album', 'camera'],
		}
		return new Promise((resolve, reject) => {
			uni.chooseImage({
				count: params.count,
				sizeType: params.sizeType,
				extension: params.extension,
				sourceType: params.sourceType,
				success: (res) => {
					console.log(res)
					resolve(res)
				},
				fail: (err) => {
					reject(err)
				}
			})
		})
	},
	// 预览图片
	previewImage(data) {
		let params = {
			current: data && data.current || 0,
			urls: data && data.urls || [],
			indicator: data && data.indicator || 'default',
			loop: data && Boolean(data.loop) || false,
			longPressActions: data && data.longPressActions || {},
		}
		if (params.urls && params.urls.length) {
			if (params.longPressActions && Object.keys(params.longPressActions).length && params.longPressActions
				.itemList && params.longPressActions.itemList.length) {
				return new Promise((resolve, reject) => {
					uni.previewImage({
						current: params.current,
						urls: params.urls,
						indicator: params.indicator,
						loop: params.loop,
						longPressActions: {
							itemList: params.longPressActions.itemList,
							itemColor: params.longPressActions.itemColor || '#000000',
							success: (res) => {
								console.log(res)
								resolve(res)
							},
							fail: (err) => {
								reject(err)
							}
						}
					})
				})
			} else {
				uni.previewImage({
					current: params.current,
					urls: params.urls,
					indicator: params.indicator,
					loop: params.loop,
				})
			}
		}
	},
	// 获取图片信息
	getImageInfo(data) {
		let params = {
			src: data && data.src || '',
		}
		return new Promise((resolve, reject) => {
			uni.getImageInfo({
				src: params.src,
				success: (res) => {
					console.log(res)
					resolve(res)
				},
				fail: (err) => {
					reject(err)
				}
			})
		})
	},
	// 保存图片到系统相册
	saveImageToPhotosAlbum(data) {
		let params = {
			filePath: data && data.filePath || '',
		}
		return new Promise((resolve, reject) => {
			uni.saveImageToPhotosAlbum({
				filePath: params.filePath,
				success: (res) => {
					console.log(res)
					resolve(res)
				},
				fail: (err) => {
					reject(err)
				}
			})
		})
	},
	deviceResult(code) {
		if (code === 0) {
			return true;
		}
		let msg;
		switch (code) {
			case 10000:
				msg = "未初始化蓝牙适配器"
				break;
			case 10001:
				msg = "当前蓝牙适配器不可用"
				break
			case 10002:
				msg = "没有找到指定设备"
				break
			case 10003:
				msg = "连接失败"
				break
			case 10004:
				msg = "没有找到指定服务"
				break
			case 10005:
				msg = "没有找到指定特征值"
				break
			case 10006:
				msg = "当前连接已断开"
				break
			case 10007:
				msg = "当前特征值不支持此操作"
				break
			case 10008:
				msg = "其余所有系统上报的异常"
				break
			case 10009:
				msg = "Android 系统特有，系统版本低于 4.3 不支持 BLE"
				break
			case 10010:
				msg = "已连接"
				break
			case 10011:
				msg = "配对设备需要配对码"
				break
			case 10012:
				msg = "连接超时"
				break
			case 10013:
				msg = "连接 deviceId 为空或者是格式不正确"
				break
		}
		uni.showToast({
			title: msg,
			icon: 'none',
		})
		return false;
	},
	// 压缩图片
	compressImage(data) {
		let params = {
			src: data && data.src || '',
			quality: data && data.quality || 80,
			width: data && data.width || 'auto',
			height: data && data.height || 'auto',
		}
		return new Promise((resolve, reject) => {
			uni.compressImage({
				src: params.src,
				quality: params.quality,
				width: params.width,
				height: params.height,
				success: (res) => {
					console.log(res)
					resolve(res)
				},
				fail: (err) => {
					reject(err)
				}
			})
		})
	},
	// 上传文件
	uploadFile(data) {
		let requestData = {
			url: data.baseURL ? data.baseURL + data.url : baseURL + data.url,
			files: data && data.files || [],
			fileType: data && data.fileType || 'image',
			file: data && data.file || '',
			filePath: data && data.filePath || '',
			name: data && data.name || 'file',
			header: data && data.header || {},
			timeout: data && data.timeout || 60000,
			formData: data && data.formData || {},
		}
		return new Promise((resolve, reject) => {
			uni.uploadFile({
				url: requestData.url,
				files: requestData.files,
				fileType: requestData.fileType,
				file: requestData.file,
				filePath: requestData.filePath,
				name: requestData.name,
				header: requestData.header,
				timeout: requestData.timeout,
				formData: requestData.formData,
				success: (res) => {
					console.log(res)
					if (res && res.statusCode == 200 && res.data) {
						resolve(res.data)
					} else {
						reject(res)
					}
				},
				fail: (err) => {
					reject(err)
				}
			})
		})
	},
	// 下载文件
	downloadFile(data) {
		let requestData = {
			url: data && data.url,
			header: data && data.header || {},
			timeout: data && data.timeout || 60000,
		}
		return new Promise((resolve, reject) => {
			uni.downloadFile({
				url: requestData.url,
				header: requestData.header,
				timeout: requestData.timeout,
				success: (res) => {
					console.log(res)
					resolve(res)
				},
				fail: (err) => {
					reject(err)
				}
			})
		})
	},
	getT(name) {
		console.log("this", this)
		//#ifdef APP-PLUS
		return this.$vm.$t(name)
		//#endif
		//#ifndef APP-PLUS
		return this.$t(name)
		//#endif
	},
	// 请求方法

	request(data) {
		let os = ''
		// #ifdef H5
		os = 'H5'
		// #endif
		// #ifdef APP-PLUS
		os = 'APP'
		// #endif
		// #ifdef MP
		os = 'MP'
		// #endif
		if (!data.header) {
			data.header = {}
		}
		// if (store && store.getters && store.getters.getToken) {
		// 	data.header.token = store.getters.getToken
		// }
		data.header.token = store.state.token
		data.header.Authorization = uni.getStorageSync('user_count_key')

		let requestData = {
			url: data.baseURL ? data.baseURL + data.url : baseURL + data.url,
			data: data && data.data || {},
			header: {
				'Content-Type': 'application/json',
				'os': os,
				...data.header
			},
			method: data && data.method || 'GET',
			timeout: data && data.timeout || 60000,
			dataType: data && data.dataType || 'json',
			responseType: data && data.responseType || 'text',
			sslVerify: data && Boolean(data.sslVerify) || true,
			withCredentials: data && Boolean(data.withCredentials) || false,
			firstIpv4: data && Boolean(data.firstIpv4) || false,
		}
		return new Promise((resolve, reject) => {
			let that = this
			that.loading = true
			try {
				uni.request({
					url: requestData.url,
					data: requestData.data,
					header: requestData.header,
					method: requestData.method,
					timeout: requestData.timeout,
					// dataType: requestData.dataType,
					// responseType: requestData.responseType,
					// sslVerify: requestData.sslVerify,
					// withCredentials: requestData.withCredentials,
					// firstIpv4: requestData.firstIpv4,
					success: (response) => {
						let res = response.data
						console.log(res);
						if (res && res.code == 200) {
							resolve(res)
						} else {

							if (res) {
								let msgObj = {
									code: '',
									msg: '',
								}
								if (Object.prototype.toString.call(res) == '[object String]') {
									let msgArr = res.replace(/{|}/g, '').replace(' ', '').split(',')
									msgArr.forEach(val => {
										if (val.indexOf('msg') != -1) {
											msgObj.msg = val.split('=')[1]
										} else if (val.indexOf('code') != -1) {
											msgObj.code = val.split('=')[1]
										}
									})
								} else if (Object.prototype.toString.call(res) ==
									'[object Object]') {
									msgObj = res
								}
								if (msgObj && msgObj.msg) {
									console.log(msgObj.code)
									if (msgObj.code == 401) {
										uni.removeStorageSync('user_count_key')
										uni.removeStorageSync('userInfo')
										uni.showToast({
											title: '登录过期'
										})
										uni.removeStorageSync('userInfo')
										uni.navigateTo({
											url: "/pages/login/login"
										})
										// if (uni.setStorageSync("token",token))
										// wx.login({
										// 	success (res) {
										// 		if (res.code) {
										// 			//发起网络请求
										// 			that.request({
										// 				url: that.auth,
										// 				data: {
										// 					appid: "wxa14895afade53c16",
										// 					code: res.code
										// 				}
										// 			}).then(result=>{
										// 				console.log(result)
										// 				console.log("2")
										//
										// 				//如果没有即是新用户
										// 				//openId: "oaKyD6zehGck6WZFL9cQchHZEuu0"
										// 				//sessionKey: "I9d5KN2lBO+8r7m3JgZTig=="
										// 				that.$store.state.wxUserInfo=result.data
										//
										// 				if (!result.data.tokenInfo){
										// 					console.log("没有授权")
										//
										// 					// that.isNewUser=true
										// 					that.getLogin()
										// 				}else{
										// 					let token=result.data.tokenInfo.tokenValue;
										// 					that.$store.state.token=token
										// 					uni.setStorageSync("token",token)
										//
										// 				}
										//
										// 			})
										// 		}
										// 	}})

									} else {
										let timer = setTimeout(() => {
											clearTimeout(timer)
											if (msgObj.msg != "访问过于频繁，请稍候再试") {
												uni.showToast({
													title: msgObj.msg,
													icon: 'none'
												})
											}
										}, 100)
									}
								}
							}
							reject(res)
						}
					},
					fail: (err) => {
						console.log(err)
						if (err == 401) {
							console.log(123)

							uni.getUserInfo({
								success: function(res) {
									console.log(res)
								}
							})
						}

						let timer = setTimeout(() => {
							clearTimeout(timer)
							uni.showToast({
								title: err.errMsg,
								icon: 'none'
							})
						}, 100)
						reject(err)
					}
				})
			} catch (error) {
				console.log(error);
				that.loading = false
			} finally {
				that.loading = false
			}
		})
	},
	getLogin() {
		// let url=this.$api.auth + '?appid=' + this.$appId + '&code=' + loginRes.code;
		// console.log(url)
		// console.log(this.$store.state.wxUserInfo)
		let data = this.$store.state.wxUserInfo;
		let that = this;
		wx.getUserInfo({
			success: function(res) {
				console.log(res)
				var userInfo = res.userInfo
				console.log(res.rawData)
				var nickName = userInfo.nickName
				var avatarUrl = userInfo.avatarUrl
				var gender = userInfo.gender //性别 0：未知、1：男、2：女
				var province = userInfo.province
				var city = userInfo.city
				var country = userInfo.country
				console.log(that.$appId)
				let openId = that.getCodeData.openId
				//解密信息
				that.request({
					url: that.$api.info,
					method: "POST",
					data: {
						"openId": openId,
						"appid": that.$appId,
						"encryptedData": res.encryptedData,
						"iv": res.iv,
						"signature": res.signature,
						"sessionKey": data.sessionKey,
					}
				}).then(res => {
					console.log(res)
					that.$store.state.wxNickName = res.data
					that.$store.state.token = res.data.tokenValue
					uni.setStorageSync("token", res.data.tokenValue)
				})

			}
		})
	}
}